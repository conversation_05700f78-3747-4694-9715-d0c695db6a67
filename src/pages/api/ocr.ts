import { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm, Fields } from 'formidable';
import fs from 'fs';
import path from 'path';
import { processDocument } from '../../utils/ocr';
import { supabase } from '../../utils/supabase';
import { v4 as uuidv4 } from 'uuid';
import { uploadDocumentToStorage } from '../../utils/supabase-storage';

export const config = {
  api: {
    bodyParser: false,
    responseLimit: false,
  },
  runtime: 'nodejs',
  maxDuration: 30,
};

type ProcessedFiles = {
  [key: string]: {
    filepath: string;
    originalFilename?: string;
    mimetype?: string;
    size?: number;
  };
};

// Allowed MIME types - expanded list to be more permissive
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/jpg',
  'image/bmp',
  'image/heic',
  'image/heif',
  'application/pdf',
  'application/octet-stream', // For browsers that don't provide a specific MIME type
];

// Max file size in bytes (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Helper to get the correct file type
const getFileType = (file: ProcessedFiles[string]): string => {
  // Use mimetype if it exists
  if (file.mimetype) {
    return file.mimetype;
  }

  // Try to determine from filename
  if (file.originalFilename) {
    const extension = path.extname(file.originalFilename).toLowerCase();
    if (['.jpg', '.jpeg'].includes(extension)) {
      return 'image/jpeg';
    } else if (extension === '.png') {
      return 'image/png';
    } else if (extension === '.pdf') {
      return 'application/pdf';
    } else if (['.heic', '.heif'].includes(extension)) {
      return 'image/heic';
    } else if (extension === '.bmp') {
      return 'image/bmp';
    }
  }

  // Default to jpeg if we can't determine
  return 'image/jpeg';
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // Set CORS headers for Vercel compatibility
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Add immediate logging to verify the handler is being called
  console.log('=== OCR API HANDLER CALLED ===');
  console.log('OCR API called with method:', req.method);
  console.log('OCR API URL:', req.url);
  console.log('OCR API query:', req.query);
  console.log('OCR API headers:', {
    contentType: req.headers['content-type'],
    contentLength: req.headers['content-length'],
    userAgent: req.headers['user-agent'],
    host: req.headers['host']
  });

  if (req.method !== 'POST') {
    console.log('OCR API: Method not allowed, received:', req.method);
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      message: `Method ${req.method} not allowed. Only POST is supported.`,
      allowedMethods: ['POST']
    });
  }

  console.log('OCR API: Processing file upload request');

  try {
    console.log('=== STEP 1: Setting up upload directory ===');
    // Enhanced form configuration for Vercel serverless environment
    const uploadDir = process.env.VERCEL ? '/tmp' : path.join(process.cwd(), 'tmp');
    console.log('Upload directory:', uploadDir);

    // Ensure upload directory exists
    if (!fs.existsSync(uploadDir)) {
      try {
        console.log('Creating upload directory...');
        fs.mkdirSync(uploadDir, { recursive: true });
        console.log('Created upload directory:', uploadDir);
      } catch (mkdirError) {
        console.error('Failed to create upload directory:', mkdirError);
        // Continue anyway, formidable might handle it
      }
    } else {
      console.log('Upload directory already exists');
    }

    console.log('=== STEP 2: Creating IncomingForm instance ===');
    console.log('Form configuration:', {
      keepExtensions: true,
      multiples: false,
      maxFileSize: MAX_FILE_SIZE,
      allowEmptyFiles: false,
      uploadDir: uploadDir,
      hashAlgorithm: false
    });

    const form = new IncomingForm({
      keepExtensions: true,
      multiples: false,
      maxFileSize: MAX_FILE_SIZE,
      allowEmptyFiles: false,
      uploadDir: uploadDir,
      filename: (_name, _ext, part) => {
        // Generate unique filename to avoid conflicts
        const uniqueFilename = `${Date.now()}-${uuidv4()}`;
        const ext = part.mimetype?.split('/').pop() || 'jpg';
        console.log('Generated filename:', `${uniqueFilename}.${ext}`);
        return `${uniqueFilename}.${ext}`;
      },
      // Additional options for Vercel compatibility
      hashAlgorithm: false
      // Removed enabledPlugins as it doesn't exist in formidable
    });

    console.log('IncomingForm instance created successfully');

    console.log('=== STEP 3: Parsing form data ===');
    const formData: { fields: Fields; files: ProcessedFiles } = await new Promise((resolve, reject) => {
      console.log('Starting form.parse...');

      form.parse(req, (err, fields, files) => {
        console.log('Form.parse callback called');

        if (err) {
          console.error('=== FORM PARSING ERROR ===');
          console.error('Error details:', {
            message: err.message,
            code: err.code,
            stack: err.stack,
            name: err.name
          });

          if (err.code === 1009) {
            return reject(new Error(`File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`));
          }
          return reject(err);
        }

        console.log('=== FORM PARSING SUCCESS ===');

        // Log detailed information about parsed files
        const fileKeys = Object.keys(files);
        console.log('Form parsed successfully, file keys:', fileKeys);
        console.log('Fields received:', Object.keys(fields));

        if (fileKeys.length === 0) {
          console.error('No files found in form data');
          return reject(new Error('No files found in the form data'));
        }

        const fileDetails = Object.entries(files).map(([key, file]) => {
          const fileObj = Array.isArray(file) ? file[0] : file;
          return {
            key,
            filepath: fileObj?.filepath || 'missing',
            filename: fileObj?.originalFilename || 'unnamed',
            mimetype: fileObj?.mimetype || 'unknown',
            size: fileObj?.size || 0
          };
        });

        console.log('Parsed files details:', JSON.stringify(fileDetails, null, 2));
        console.log('=== STEP 3 COMPLETED: Form parsing successful ===');
        resolve({ fields, files: files as unknown as ProcessedFiles });
      });
    });

    console.log('=== STEP 4: Processing agent ID and file extraction ===');

    // Get agent ID from request cookies or query params, ensuring it's a string
    const agentIdParam = req.cookies.agentId || req.query.agentId;
    const agentId = typeof agentIdParam === 'string' ? agentIdParam :
                    Array.isArray(agentIdParam) ? agentIdParam[0] :
                    uuidv4();

    console.log('Using agent ID:', agentId);

    const fileKey = Object.keys(formData.files)[0];
    console.log('First file key:', fileKey);

    if (!fileKey) {
      console.error('OCR API: No file found in upload');
      return res.status(400).json({
        success: false,
        error: 'No file uploaded',
        message: 'No file was found in the upload'
      });
    }

    console.log('=== STEP 5: Extracting file from form data ===');

    // Handle both array and single file formats from formidable
    let file = formData.files[fileKey];
    console.log('Raw file object type:', Array.isArray(file) ? 'array' : typeof file);

    if (Array.isArray(file)) {
      console.log('File is array, taking first element');
      file = file[0];
    }

    console.log('File received:', {
      filename: file.originalFilename || 'unnamed',
      mimetype: file.mimetype || 'unknown',
      filepath: file.filepath ? 'exists' : 'missing',
      size: file.size ? `${(file.size / (1024 * 1024)).toFixed(2)} MB` : 'unknown'
    });

    console.log('=== STEP 5 COMPLETED: File extracted successfully ===');

    console.log('=== STEP 6: Validating file object and filepath ===');

    // Validate file object and filepath
    if (!file || !file.filepath || !fs.existsSync(file.filepath)) {
      console.error('OCR API: Invalid file or filepath');
      console.error('Validation details:', {
        fileExists: !!file,
        filepathExists: !!file?.filepath,
        fileSystemExists: file?.filepath ? fs.existsSync(file.filepath) : false,
        filepath: file?.filepath || 'undefined'
      });

      return res.status(400).json({
        success: false,
        error: 'Invalid file upload',
        message: 'The uploaded file is invalid or cannot be processed'
      });
    }

    console.log('=== STEP 7: Getting file stats ===');

    // Get file stats to verify it exists and has size
    try {
      const stats = fs.statSync(file.filepath);
      console.log('File stats:', {
        size: stats.size,
        isFile: stats.isFile(),
        created: stats.birthtime,
        modified: stats.mtime
      });

      if (!stats.isFile() || stats.size === 0) {
        console.error('OCR API: File is empty or not a file');
        return res.status(400).json({
          success: false,
          error: 'Invalid file',
          message: 'The uploaded file is empty or invalid'
        });
      }

      console.log('=== STEP 7 COMPLETED: File stats validated ===');
    } catch (statError) {
      console.error('OCR API: Error checking file stats:', statError);
      return res.status(400).json({
        success: false,
        error: 'File stat error',
        message: 'Error verifying the uploaded file'
      });
    }

    console.log('=== STEP 8: Determining file type ===');

    // Try to determine file type from extension if MIME type is generic
    const fileType = getFileType(file);
    console.log('Detected file type:', fileType);
    console.log('Allowed MIME types:', ALLOWED_MIME_TYPES);

    // Validate file type - be more permissive
    if (!ALLOWED_MIME_TYPES.includes(fileType)) {
      console.error('OCR API: Invalid file type', { mimetype: fileType });

      // Clean up temp file
      try {
        fs.unlinkSync(file.filepath);
        console.log('Cleaned up temp file after type validation failure');
      } catch (cleanupError) {
        console.warn('Failed to clean up temp file:', cleanupError);
      }

      return res.status(400).json({
        success: false,
        error: 'Invalid file type',
        message: `File type ${fileType} is not supported. Please upload a JPG, PNG, or PDF file.`
      });
    }

    console.log('=== STEP 8 COMPLETED: File type validated ===');

    try {
      console.log('=== STEP 9: Final file validation and OCR processing ===');

      // Read file from temporary storage
      console.log('Reading file data...');
      // We don't need to read the file into memory, just use the filepath directly
      console.log('File filepath:', file.filepath);

      // Validate file exists
      if (!fs.existsSync(file.filepath)) {
        throw new Error('File does not exist at provided path');
      }

      // Get file stats to verify size
      const stats = fs.statSync(file.filepath);
      if (stats.size === 0) {
        throw new Error('File data is empty');
      }
      console.log('File size:', stats.size);

      // Create a file name if one doesn't exist
      const fileName = file.originalFilename || `document.${fileType.split('/')[1]}`;
      console.log('Using filename:', fileName);

      console.log('=== STEP 10: Processing document with enhanced OCR ===');
      console.log('Calling processDocument with:', {
        filepath: file.filepath,
        fileType: fileType
      });

      // Extract document data using enhanced processing (supports PDF and images)
      const extractedData = await processDocument(file.filepath);
      console.log('OCR extraction complete');
      console.log('Extracted data keys:', Object.keys(extractedData || {}));

      console.log('=== STEP 11: Uploading to Supabase storage ===');

      // Upload file to Supabase storage using file path directly
      console.log('Uploading to Supabase storage...');
      console.log('Upload parameters:', {
        filepath: file.filepath,
        agentId: agentId,
        documentType: 'passport',
        fileName: fileName,
        fileType: fileType
      });

      const { filePath, error: uploadError } = await uploadDocumentToStorage(
        file.filepath,  // Pass the file path directly
        agentId,
        'passport',
        fileName,
        fileType
      );

      if (uploadError) {
        console.error('Upload error:', uploadError);
        throw new Error(`Failed to upload file to storage: ${uploadError.message}`);
      }
      console.log('File uploaded successfully:', filePath);
      console.log('=== STEP 11 COMPLETED: File uploaded to storage ===');

      console.log('=== STEP 12: Logging to database ===');

      // Log OCR processing to database
      const { error: insertError } = await supabase.from('ocr_processing_history').insert({
        agent_id: agentId,
        document_type: 'passport',
        document_path: filePath,
        processing_status: 'success',
        extracted_data: extractedData,
      });

      if (insertError) {
        console.warn('Failed to log OCR processing:', insertError);
      } else {
        console.log('OCR processing logged successfully');
      }

      console.log('=== STEP 13: Cleaning up temp file ===');

      // Clean up temp file
      try {
        fs.unlinkSync(file.filepath);
        console.log('Temp file cleaned up successfully');
      } catch (cleanupError) {
        console.warn('Failed to clean up temp file:', cleanupError);
      }

      console.log('=== STEP 14: Returning success response ===');

      // Return the extracted data along with the file path
      return res.status(200).json({
        success: true,
        data: extractedData,
        filePath,
        agentId,
        message: 'Document processed successfully'
      });
    } catch (fileError) {
      console.error('=== FILE PROCESSING ERROR ===');
      console.error('File processing error:', fileError);
      console.error('Error details:', {
        message: fileError instanceof Error ? fileError.message : 'Unknown error',
        stack: fileError instanceof Error ? fileError.stack : 'No stack trace',
        name: fileError instanceof Error ? fileError.name : 'Unknown error type'
      });

      // Log OCR processing error to database
      try {
        console.log('Logging error to database...');
        await supabase.from('ocr_processing_history').insert({
          agent_id: agentId || 'unknown',
          document_type: 'passport',
          document_path: 'failed_upload',
          processing_status: 'error',
          error_message: fileError instanceof Error ? fileError.message : 'Unknown file processing error',
        });
        console.log('Error logged to database successfully');
      } catch (logError) {
        console.error('Failed to log OCR error:', logError);
      }

      // Clean up temp file if it exists
      if (file && file.filepath) {
        try {
          console.log('Cleaning up temp file after error...');
          fs.unlinkSync(file.filepath);
          console.log('Temp file cleaned up after error');
        } catch (cleanupError) {
          console.warn('Failed to clean up temp file after error:', cleanupError);
        }
      }

      return res.status(500).json({
        success: false,
        error: 'File processing error',
        message: fileError instanceof Error ? fileError.message : 'Failed to process the uploaded file'
      });
    }
  } catch (error) {
    console.error('=== GENERAL OCR API ERROR ===');
    console.error('OCR API error:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      name: error instanceof Error ? error.name : 'Unknown error type'
    });

    return res.status(500).json({
      success: false,
      error: 'Failed to process document',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default handler;